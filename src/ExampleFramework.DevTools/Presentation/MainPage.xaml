<Page x:Class="ExampleFramework.DevTools.Presentation.MainPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:local="using:ExampleFramework.DevTools.Presentation"
      xmlns:uen="using:Uno.Extensions.Navigation.UI"
      xmlns:utu="using:Uno.Toolkit.UI"
      NavigationCacheMode="Required"
      Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
  <ScrollViewer IsTabStop="True">
    <Grid utu:SafeArea.Insets="VisibleBounds">
      <Grid.RowDefinitions>
        <RowDefinition Height="Auto" />
        <RowDefinition />
      </Grid.RowDefinitions>
        
      <utu:NavigationBar Content="{Binding Title}" />

      <StackPanel Grid.Row="1"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Spacing="16">
        <TextBox Text="{Binding Name, Mode=TwoWay}"
            PlaceholderText="Enter your name:" />
        <Button Content="Go to Second Page"
            AutomationProperties.AutomationId="SecondPageButton"
            Command="{Binding GoToSecond}" />
      </StackPanel>
        
        
    </Grid>
  </ScrollViewer>
</Page>
