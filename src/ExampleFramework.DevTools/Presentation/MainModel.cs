namespace ExampleFramework.DevTools.Presentation;

public partial record MainModel
{
    private readonly INavigator _navigator;

    public MainModel(IOptions<AppConfig> appInfo, INavigator navigator)
    {
        _navigator = navigator;
        Title = "Main";
        Title += $" - {appInfo?.Value?.Environment}";
    }

    public string? Title { get; }

    public IState<string> Name => State<string>.Value(this, () => string.Empty);
}
