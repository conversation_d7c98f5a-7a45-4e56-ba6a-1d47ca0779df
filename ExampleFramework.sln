
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33213.308
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ExampleFramework", "src\ExampleFramework\ExampleFramework.csproj", "{D0041716-74DA-45E3-A0D3-B4CC5CAC766B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "samples", "samples", "{D2D79087-63DE-48DA-A903-D915B1FAE013}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "maui", "maui", "{2D3F8C1A-337A-4913-A321-5167F7B49CE0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "visual-test-utils", "visual-test-utils", "{2BCEA886-59E5-4D28-AA05-3310CBB59C55}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VisualTestUtils", "src\visual-test-utils\VisualTestUtils\VisualTestUtils.csproj", "{70580142-F7FB-4266-8B0A-0E5174EDB812}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VisualTestUtils.AppConnector", "src\visual-test-utils\VisualTestUtils.AppConnector\VisualTestUtils.AppConnector.csproj", "{AF981B9B-883D-4343-A2FD-313343A3AE29}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VisualTestUtils.ImageHash", "src\visual-test-utils\VisualTestUtils.ImageHash\VisualTestUtils.ImageHash.csproj", "{6F7B5A01-752B-4948-93CA-8B8670E3F052}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VisualTestUtils.MagickNet", "src\visual-test-utils\VisualTestUtils.MagickNet\VisualTestUtils.MagickNet.csproj", "{21CE0B2C-F8A4-4952-A305-F88403ACBF91}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExampleFramework.Maui", "src\ExampleFramework.Maui\ExampleFramework.Maui.csproj", "{70FC4263-9C01-46E6-B884-CF39E170A9EF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExampleFramework.Tooling", "src\ExampleFramework.Tooling\ExampleFramework.Tooling.csproj", "{2B1E5950-1519-47EE-92E3-6FEA470EAF2B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DefaultTemplateWithContent", "samples\maui\DefaultTemplateWithContent\DefaultTemplateWithContent.csproj", "{CC0F1259-82DC-4437-AC2D-946A8B5F5746}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EcommerceMAUI", "samples\maui\EcommerceMAUI\EcommerceMAUI.csproj", "{E62416C2-7DFE-DFAC-8E0C-34860B1ED88F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExampleFramework.Wpf", "src\ExampleFramework.Wpf\ExampleFramework.Wpf.csproj", "{D557CE7D-B7FA-4CBA-96A3-55B5634EAB94}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "platforms", "platforms", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{7DC59701-DFBE-41F0-A8E8-09D7D49D77DE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ExampleFramework.DevTools", "src\ExampleFramework.DevTools\ExampleFramework.DevTools.csproj", "{1609025D-27AF-22BE-3E93-6829BFDCCC68}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D0041716-74DA-45E3-A0D3-B4CC5CAC766B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0041716-74DA-45E3-A0D3-B4CC5CAC766B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0041716-74DA-45E3-A0D3-B4CC5CAC766B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0041716-74DA-45E3-A0D3-B4CC5CAC766B}.Release|Any CPU.Build.0 = Release|Any CPU
		{70580142-F7FB-4266-8B0A-0E5174EDB812}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{70580142-F7FB-4266-8B0A-0E5174EDB812}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{70580142-F7FB-4266-8B0A-0E5174EDB812}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{70580142-F7FB-4266-8B0A-0E5174EDB812}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF981B9B-883D-4343-A2FD-313343A3AE29}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF981B9B-883D-4343-A2FD-313343A3AE29}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF981B9B-883D-4343-A2FD-313343A3AE29}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF981B9B-883D-4343-A2FD-313343A3AE29}.Release|Any CPU.Build.0 = Release|Any CPU
		{6F7B5A01-752B-4948-93CA-8B8670E3F052}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F7B5A01-752B-4948-93CA-8B8670E3F052}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F7B5A01-752B-4948-93CA-8B8670E3F052}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F7B5A01-752B-4948-93CA-8B8670E3F052}.Release|Any CPU.Build.0 = Release|Any CPU
		{21CE0B2C-F8A4-4952-A305-F88403ACBF91}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{21CE0B2C-F8A4-4952-A305-F88403ACBF91}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{21CE0B2C-F8A4-4952-A305-F88403ACBF91}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{21CE0B2C-F8A4-4952-A305-F88403ACBF91}.Release|Any CPU.Build.0 = Release|Any CPU
		{70FC4263-9C01-46E6-B884-CF39E170A9EF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{70FC4263-9C01-46E6-B884-CF39E170A9EF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{70FC4263-9C01-46E6-B884-CF39E170A9EF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{70FC4263-9C01-46E6-B884-CF39E170A9EF}.Release|Any CPU.Build.0 = Release|Any CPU
		{2B1E5950-1519-47EE-92E3-6FEA470EAF2B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2B1E5950-1519-47EE-92E3-6FEA470EAF2B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2B1E5950-1519-47EE-92E3-6FEA470EAF2B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2B1E5950-1519-47EE-92E3-6FEA470EAF2B}.Release|Any CPU.Build.0 = Release|Any CPU
		{CC0F1259-82DC-4437-AC2D-946A8B5F5746}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CC0F1259-82DC-4437-AC2D-946A8B5F5746}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CC0F1259-82DC-4437-AC2D-946A8B5F5746}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{CC0F1259-82DC-4437-AC2D-946A8B5F5746}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CC0F1259-82DC-4437-AC2D-946A8B5F5746}.Release|Any CPU.Build.0 = Release|Any CPU
		{E62416C2-7DFE-DFAC-8E0C-34860B1ED88F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E62416C2-7DFE-DFAC-8E0C-34860B1ED88F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E62416C2-7DFE-DFAC-8E0C-34860B1ED88F}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{E62416C2-7DFE-DFAC-8E0C-34860B1ED88F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E62416C2-7DFE-DFAC-8E0C-34860B1ED88F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E62416C2-7DFE-DFAC-8E0C-34860B1ED88F}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{D557CE7D-B7FA-4CBA-96A3-55B5634EAB94}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D557CE7D-B7FA-4CBA-96A3-55B5634EAB94}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D557CE7D-B7FA-4CBA-96A3-55B5634EAB94}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D557CE7D-B7FA-4CBA-96A3-55B5634EAB94}.Release|Any CPU.Build.0 = Release|Any CPU
		{1609025D-27AF-22BE-3E93-6829BFDCCC68}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1609025D-27AF-22BE-3E93-6829BFDCCC68}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1609025D-27AF-22BE-3E93-6829BFDCCC68}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1609025D-27AF-22BE-3E93-6829BFDCCC68}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{2D3F8C1A-337A-4913-A321-5167F7B49CE0} = {D2D79087-63DE-48DA-A903-D915B1FAE013}
		{70580142-F7FB-4266-8B0A-0E5174EDB812} = {2BCEA886-59E5-4D28-AA05-3310CBB59C55}
		{AF981B9B-883D-4343-A2FD-313343A3AE29} = {2BCEA886-59E5-4D28-AA05-3310CBB59C55}
		{6F7B5A01-752B-4948-93CA-8B8670E3F052} = {2BCEA886-59E5-4D28-AA05-3310CBB59C55}
		{21CE0B2C-F8A4-4952-A305-F88403ACBF91} = {2BCEA886-59E5-4D28-AA05-3310CBB59C55}
		{CC0F1259-82DC-4437-AC2D-946A8B5F5746} = {2D3F8C1A-337A-4913-A321-5167F7B49CE0}
		{E62416C2-7DFE-DFAC-8E0C-34860B1ED88F} = {2D3F8C1A-337A-4913-A321-5167F7B49CE0}
		{02EA681E-C7D8-13C7-8484-4AC65E1B71E8} = {7DC59701-DFBE-41F0-A8E8-09D7D49D77DE}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {52CB1B96-37FD-4F1F-9488-5F8381E0A631}
	EndGlobalSection
EndGlobal
